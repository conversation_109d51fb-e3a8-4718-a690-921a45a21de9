import { PrismaClient } from '@prisma/client';
import { hashPassword } from '../utils/auth';

const prisma = new PrismaClient();

async function main() {
  console.log('🌱 Starting database seed...');

  // Create admin user
  const adminPassword = await hashPassword('admin123');
  const admin = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      password: adminPassword,
      firstName: 'Admin',
      lastName: 'User',
      role: 'ADMIN',
    },
  });

  // Create manager user
  const managerPassword = await hashPassword('manager123');
  const manager = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      password: managerPassword,
      firstName: 'Manager',
      lastName: 'User',
      role: 'MANAGER',
    },
  });

  // Create regular user
  const userPassword = await hashPassword('user123');
  const user = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      password: userPassword,
      firstName: 'Regular',
      lastName: 'User',
      role: 'USER',
    },
  });

  console.log('👥 Created users:', { admin: admin.email, manager: manager.email, user: user.email });

  // Create sample clients
  const client1 = await prisma.client.upsert({
    where: { id: 'sample-client-1' },
    update: {},
    create: {
      id: 'sample-client-1',
      name: 'Acme Corporation',
      email: '<EMAIL>',
      phone: '******-0123',
      website: 'https://acme.com',
      description: 'A leading technology company specializing in innovative solutions.',
      status: 'ACTIVE',
      createdById: admin.id,
    },
  });

  const client2 = await prisma.client.upsert({
    where: { id: 'sample-client-2' },
    update: {},
    create: {
      id: 'sample-client-2',
      name: 'TechStart Inc.',
      email: '<EMAIL>',
      phone: '******-0456',
      website: 'https://techstart.io',
      description: 'An innovative startup focused on digital transformation.',
      status: 'ACTIVE',
      createdById: manager.id,
    },
  });

  console.log('🏢 Created clients:', { client1: client1.name, client2: client2.name });

  // Create sample projects
  const project1 = await prisma.project.upsert({
    where: { id: 'sample-project-1' },
    update: {},
    create: {
      id: 'sample-project-1',
      name: 'Website Redesign',
      description: 'Complete redesign of the company website with modern UI/UX.',
      status: 'IN_PROGRESS',
      priority: 'HIGH',
      startDate: new Date('2024-01-01'),
      endDate: new Date('2024-03-31'),
      budget: 25000,
      color: '#3B82F6',
      clientId: client1.id,
      createdById: admin.id,
    },
  });

  const project2 = await prisma.project.upsert({
    where: { id: 'sample-project-2' },
    update: {},
    create: {
      id: 'sample-project-2',
      name: 'Mobile App Development',
      description: 'Development of a cross-platform mobile application.',
      status: 'PLANNING',
      priority: 'MEDIUM',
      startDate: new Date('2024-02-01'),
      endDate: new Date('2024-06-30'),
      budget: 50000,
      color: '#10B981',
      clientId: client1.id,
      createdById: manager.id,
    },
  });

  const project3 = await prisma.project.upsert({
    where: { id: 'sample-project-3' },
    update: {},
    create: {
      id: 'sample-project-3',
      name: 'Digital Marketing Campaign',
      description: 'Comprehensive digital marketing strategy and implementation.',
      status: 'IN_PROGRESS',
      priority: 'MEDIUM',
      startDate: new Date('2024-01-15'),
      endDate: new Date('2024-04-15'),
      budget: 15000,
      color: '#F59E0B',
      clientId: client2.id,
      createdById: admin.id,
    },
  });

  console.log('📋 Created projects:', { 
    project1: project1.name, 
    project2: project2.name, 
    project3: project3.name 
  });

  // Create sample tasks
  const tasks = [
    {
      id: 'sample-task-1',
      title: 'Design Homepage Mockup',
      description: 'Create wireframes and high-fidelity mockups for the new homepage.',
      status: 'COMPLETED',
      priority: 'HIGH',
      dueDate: new Date('2024-01-15'),
      estimatedHours: 16,
      actualHours: 18,
      tags: ['design', 'ui/ux', 'homepage'],
      projectId: project1.id,
      assignedToId: user.id,
      createdById: admin.id,
    },
    {
      id: 'sample-task-2',
      title: 'Implement Responsive Navigation',
      description: 'Code the responsive navigation component for all device sizes.',
      status: 'IN_PROGRESS',
      priority: 'HIGH',
      dueDate: new Date('2024-01-25'),
      estimatedHours: 12,
      tags: ['development', 'frontend', 'responsive'],
      projectId: project1.id,
      assignedToId: manager.id,
      createdById: admin.id,
    },
    {
      id: 'sample-task-3',
      title: 'Market Research Analysis',
      description: 'Conduct comprehensive market research for the mobile app.',
      status: 'TODO',
      priority: 'MEDIUM',
      dueDate: new Date('2024-02-10'),
      estimatedHours: 24,
      tags: ['research', 'analysis', 'market'],
      projectId: project2.id,
      assignedToId: user.id,
      createdById: manager.id,
    },
    {
      id: 'sample-task-4',
      title: 'Social Media Strategy',
      description: 'Develop social media content strategy and posting schedule.',
      status: 'IN_PROGRESS',
      priority: 'MEDIUM',
      dueDate: new Date('2024-02-01'),
      estimatedHours: 8,
      tags: ['marketing', 'social-media', 'strategy'],
      projectId: project3.id,
      assignedToId: user.id,
      createdById: admin.id,
    },
  ];

  for (const taskData of tasks) {
    await prisma.task.upsert({
      where: { id: taskData.id },
      update: {},
      create: taskData,
    });
  }

  console.log('✅ Created tasks:', tasks.length);

  console.log('🎉 Database seed completed successfully!');
  console.log('\n📝 Sample login credentials:');
  console.log('Admin: <EMAIL> / admin123');
  console.log('Manager: <EMAIL> / manager123');
  console.log('User: <EMAIL> / user123');
}

main()
  .catch((e) => {
    console.error('❌ Seed failed:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
