import { Response } from 'express';
import { prisma } from '../utils/database';
import { createError } from '../middleware/errorHandler';
import { AuthenticatedRequest, CreateAssetDto, AssetQuery } from '../types';
import multer from 'multer';
import path from 'path';
import fs from 'fs';

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    const uploadDir = process.env.UPLOAD_DIR || 'uploads';
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }
    cb(null, uploadDir);
  },
  filename: (req, file, cb) => {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, file.fieldname + '-' + uniqueSuffix + path.extname(file.originalname));
  },
});

const upload = multer({
  storage,
  limits: {
    fileSize: parseInt(process.env.MAX_FILE_SIZE || '10485760'), // 10MB default
  },
  fileFilter: (req, file, cb) => {
    // Allow all file types for now, but you can add restrictions here
    cb(null, true);
  },
});

export const uploadMiddleware = upload.single('file');

export const getAssets = async (req: AuthenticatedRequest, res: Response) => {
  const { 
    page = '1', 
    limit = '10', 
    sortBy = 'createdAt', 
    sortOrder = 'desc', 
    clientId, 
    projectId, 
    taskId, 
    mimeType, 
    search 
  }: AssetQuery = req.query;

  const pageNum = parseInt(page);
  const limitNum = parseInt(limit);
  const skip = (pageNum - 1) * limitNum;

  const where: any = {};

  if (clientId) {
    where.clientId = clientId;
  }

  if (projectId) {
    where.projectId = projectId;
  }

  if (taskId) {
    where.taskId = taskId;
  }

  if (mimeType) {
    where.mimeType = { contains: mimeType };
  }

  if (search) {
    where.OR = [
      { filename: { contains: search, mode: 'insensitive' } },
      { originalName: { contains: search, mode: 'insensitive' } },
      { description: { contains: search, mode: 'insensitive' } },
    ];
  }

  const [assets, total] = await Promise.all([
    prisma.asset.findMany({
      where,
      skip,
      take: limitNum,
      orderBy: { [sortBy]: sortOrder },
      include: {
        client: {
          select: { id: true, name: true },
        },
        project: {
          select: { id: true, name: true },
        },
        task: {
          select: { id: true, title: true },
        },
        createdBy: {
          select: { id: true, firstName: true, lastName: true, email: true },
        },
      },
    }),
    prisma.asset.count({ where }),
  ]);

  res.json({
    success: true,
    data: assets,
    pagination: {
      page: pageNum,
      limit: limitNum,
      total,
      totalPages: Math.ceil(total / limitNum),
    },
  });
};

export const getAssetById = async (req: AuthenticatedRequest, res: Response) => {
  const { id } = req.params;

  const asset = await prisma.asset.findUnique({
    where: { id },
    include: {
      client: {
        select: { id: true, name: true },
      },
      project: {
        select: { id: true, name: true },
      },
      task: {
        select: { id: true, title: true },
      },
      createdBy: {
        select: { id: true, firstName: true, lastName: true, email: true },
      },
    },
  });

  if (!asset) {
    throw createError('Asset not found', 404);
  }

  res.json({
    success: true,
    data: asset,
  });
};

export const uploadAsset = async (req: AuthenticatedRequest, res: Response) => {
  uploadMiddleware(req, res, async (err) => {
    if (err) {
      if (err instanceof multer.MulterError) {
        if (err.code === 'LIMIT_FILE_SIZE') {
          throw createError('File too large', 400);
        }
      }
      throw createError('File upload failed', 400);
    }

    if (!req.file) {
      throw createError('No file uploaded', 400);
    }

    const { clientId, projectId, taskId, description, tags } = req.body;
    const userId = req.user!.id;

    // Validate associations
    if (clientId) {
      const client = await prisma.client.findUnique({ where: { id: clientId } });
      if (!client) {
        throw createError('Client not found', 404);
      }
    }

    if (projectId) {
      const project = await prisma.project.findUnique({ where: { id: projectId } });
      if (!project) {
        throw createError('Project not found', 404);
      }
    }

    if (taskId) {
      const task = await prisma.task.findUnique({ where: { id: taskId } });
      if (!task) {
        throw createError('Task not found', 404);
      }
    }

    const asset = await prisma.asset.create({
      data: {
        filename: req.file.filename,
        originalName: req.file.originalname,
        mimeType: req.file.mimetype,
        size: req.file.size,
        url: `/uploads/${req.file.filename}`,
        description: description || null,
        tags: tags ? JSON.parse(tags) : [],
        clientId: clientId || null,
        projectId: projectId || null,
        taskId: taskId || null,
        createdById: userId,
      },
      include: {
        client: {
          select: { id: true, name: true },
        },
        project: {
          select: { id: true, name: true },
        },
        task: {
          select: { id: true, title: true },
        },
        createdBy: {
          select: { id: true, firstName: true, lastName: true, email: true },
        },
      },
    });

    res.status(201).json({
      success: true,
      data: asset,
      message: 'Asset uploaded successfully',
    });
  });
};

export const updateAsset = async (req: AuthenticatedRequest, res: Response) => {
  const { id } = req.params;
  const { description, tags, clientId, projectId, taskId } = req.body;

  const existingAsset = await prisma.asset.findUnique({
    where: { id },
  });

  if (!existingAsset) {
    throw createError('Asset not found', 404);
  }

  // Validate new associations if provided
  if (clientId) {
    const client = await prisma.client.findUnique({ where: { id: clientId } });
    if (!client) {
      throw createError('Client not found', 404);
    }
  }

  if (projectId) {
    const project = await prisma.project.findUnique({ where: { id: projectId } });
    if (!project) {
      throw createError('Project not found', 404);
    }
  }

  if (taskId) {
    const task = await prisma.task.findUnique({ where: { id: taskId } });
    if (!task) {
      throw createError('Task not found', 404);
    }
  }

  const asset = await prisma.asset.update({
    where: { id },
    data: {
      description: description !== undefined ? description : existingAsset.description,
      tags: tags !== undefined ? tags : existingAsset.tags,
      clientId: clientId !== undefined ? clientId : existingAsset.clientId,
      projectId: projectId !== undefined ? projectId : existingAsset.projectId,
      taskId: taskId !== undefined ? taskId : existingAsset.taskId,
    },
    include: {
      client: {
        select: { id: true, name: true },
      },
      project: {
        select: { id: true, name: true },
      },
      task: {
        select: { id: true, title: true },
      },
      createdBy: {
        select: { id: true, firstName: true, lastName: true, email: true },
      },
    },
  });

  res.json({
    success: true,
    data: asset,
    message: 'Asset updated successfully',
  });
};

export const deleteAsset = async (req: AuthenticatedRequest, res: Response) => {
  const { id } = req.params;

  const existingAsset = await prisma.asset.findUnique({
    where: { id },
  });

  if (!existingAsset) {
    throw createError('Asset not found', 404);
  }

  // Delete the file from filesystem
  const uploadDir = process.env.UPLOAD_DIR || 'uploads';
  const filePath = path.join(uploadDir, existingAsset.filename);
  
  if (fs.existsSync(filePath)) {
    fs.unlinkSync(filePath);
  }

  await prisma.asset.delete({
    where: { id },
  });

  res.json({
    success: true,
    message: 'Asset deleted successfully',
  });
};
