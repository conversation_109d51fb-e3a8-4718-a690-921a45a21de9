import { Response } from 'express';
import { prisma } from '../utils/database';
import { createError } from '../middleware/errorHandler';
import { AuthenticatedRequest, CreateTaskDto, UpdateTaskDto, TaskQuery } from '../types';

export const getTasks = async (req: AuthenticatedRequest, res: Response) => {
  const { 
    page = '1', 
    limit = '10', 
    sortBy = 'createdAt', 
    sortOrder = 'desc', 
    projectId, 
    assignedToId, 
    status, 
    priority, 
    search 
  }: TaskQuery = req.query;

  const pageNum = parseInt(page);
  const limitNum = parseInt(limit);
  const skip = (pageNum - 1) * limitNum;

  const where: any = {};

  if (projectId) {
    where.projectId = projectId;
  }

  if (assignedToId) {
    where.assignedToId = assignedToId;
  }

  if (status) {
    where.status = status;
  }

  if (priority) {
    where.priority = priority;
  }

  if (search) {
    where.OR = [
      { title: { contains: search, mode: 'insensitive' } },
      { description: { contains: search, mode: 'insensitive' } },
    ];
  }

  const [tasks, total] = await Promise.all([
    prisma.task.findMany({
      where,
      skip,
      take: limitNum,
      orderBy: { [sortBy]: sortOrder },
      include: {
        project: {
          select: { id: true, name: true, color: true, client: { select: { id: true, name: true } } },
        },
        assignedTo: {
          select: { id: true, firstName: true, lastName: true, email: true, avatar: true },
        },
        createdBy: {
          select: { id: true, firstName: true, lastName: true, email: true },
        },
        _count: {
          select: { assets: true },
        },
      },
    }),
    prisma.task.count({ where }),
  ]);

  res.json({
    success: true,
    data: tasks,
    pagination: {
      page: pageNum,
      limit: limitNum,
      total,
      totalPages: Math.ceil(total / limitNum),
    },
  });
};

export const getTaskById = async (req: AuthenticatedRequest, res: Response) => {
  const { id } = req.params;

  const task = await prisma.task.findUnique({
    where: { id },
    include: {
      project: {
        select: { 
          id: true, 
          name: true, 
          color: true, 
          client: { select: { id: true, name: true, logo: true } } 
        },
      },
      assignedTo: {
        select: { id: true, firstName: true, lastName: true, email: true, avatar: true },
      },
      createdBy: {
        select: { id: true, firstName: true, lastName: true, email: true },
      },
      assets: {
        include: {
          createdBy: {
            select: { id: true, firstName: true, lastName: true, email: true },
          },
        },
      },
    },
  });

  if (!task) {
    throw createError('Task not found', 404);
  }

  res.json({
    success: true,
    data: task,
  });
};

export const createTask = async (req: AuthenticatedRequest, res: Response) => {
  const data: CreateTaskDto = req.body;
  const userId = req.user!.id;

  // Verify project exists
  const project = await prisma.project.findUnique({
    where: { id: data.projectId },
  });

  if (!project) {
    throw createError('Project not found', 404);
  }

  // Verify assigned user exists if provided
  if (data.assignedToId) {
    const assignedUser = await prisma.user.findUnique({
      where: { id: data.assignedToId },
    });

    if (!assignedUser) {
      throw createError('Assigned user not found', 404);
    }
  }

  const task = await prisma.task.create({
    data: {
      ...data,
      dueDate: data.dueDate ? new Date(data.dueDate) : null,
      createdById: userId,
    },
    include: {
      project: {
        select: { 
          id: true, 
          name: true, 
          color: true, 
          client: { select: { id: true, name: true } } 
        },
      },
      assignedTo: {
        select: { id: true, firstName: true, lastName: true, email: true, avatar: true },
      },
      createdBy: {
        select: { id: true, firstName: true, lastName: true, email: true },
      },
    },
  });

  res.status(201).json({
    success: true,
    data: task,
    message: 'Task created successfully',
  });
};

export const updateTask = async (req: AuthenticatedRequest, res: Response) => {
  const { id } = req.params;
  const data: UpdateTaskDto = req.body;

  const existingTask = await prisma.task.findUnique({
    where: { id },
  });

  if (!existingTask) {
    throw createError('Task not found', 404);
  }

  // If projectId is being updated, verify the new project exists
  if (data.projectId) {
    const project = await prisma.project.findUnique({
      where: { id: data.projectId },
    });

    if (!project) {
      throw createError('Project not found', 404);
    }
  }

  // If assignedToId is being updated, verify the user exists
  if (data.assignedToId) {
    const assignedUser = await prisma.user.findUnique({
      where: { id: data.assignedToId },
    });

    if (!assignedUser) {
      throw createError('Assigned user not found', 404);
    }
  }

  const updateData: any = { ...data };
  if (data.dueDate) {
    updateData.dueDate = new Date(data.dueDate);
  }

  const task = await prisma.task.update({
    where: { id },
    data: updateData,
    include: {
      project: {
        select: { 
          id: true, 
          name: true, 
          color: true, 
          client: { select: { id: true, name: true } } 
        },
      },
      assignedTo: {
        select: { id: true, firstName: true, lastName: true, email: true, avatar: true },
      },
      createdBy: {
        select: { id: true, firstName: true, lastName: true, email: true },
      },
    },
  });

  res.json({
    success: true,
    data: task,
    message: 'Task updated successfully',
  });
};

export const deleteTask = async (req: AuthenticatedRequest, res: Response) => {
  const { id } = req.params;

  const existingTask = await prisma.task.findUnique({
    where: { id },
  });

  if (!existingTask) {
    throw createError('Task not found', 404);
  }

  await prisma.task.delete({
    where: { id },
  });

  res.json({
    success: true,
    message: 'Task deleted successfully',
  });
};

export const getTaskAssets = async (req: AuthenticatedRequest, res: Response) => {
  const { id } = req.params;
  const { page = '1', limit = '10', sortBy = 'createdAt', sortOrder = 'desc' } = req.query;

  const pageNum = parseInt(page as string);
  const limitNum = parseInt(limit as string);
  const skip = (pageNum - 1) * limitNum;

  const task = await prisma.task.findUnique({
    where: { id },
  });

  if (!task) {
    throw createError('Task not found', 404);
  }

  const [assets, total] = await Promise.all([
    prisma.asset.findMany({
      where: { taskId: id },
      skip,
      take: limitNum,
      orderBy: { [sortBy as string]: sortOrder },
      include: {
        createdBy: {
          select: { id: true, firstName: true, lastName: true, email: true },
        },
      },
    }),
    prisma.asset.count({ where: { taskId: id } }),
  ]);

  res.json({
    success: true,
    data: assets,
    pagination: {
      page: pageNum,
      limit: limitNum,
      total,
      totalPages: Math.ceil(total / limitNum),
    },
  });
};
