import { Response } from 'express';
import { prisma } from '../utils/database';
import { createError } from '../middleware/errorHandler';
import { AuthenticatedRequest, CreateClientDto, UpdateClientDto, ClientQuery } from '../types';

export const getClients = async (req: AuthenticatedRequest, res: Response) => {
  const { page = '1', limit = '10', sortBy = 'createdAt', sortOrder = 'desc', status, search }: ClientQuery = req.query;

  const pageNum = parseInt(page);
  const limitNum = parseInt(limit);
  const skip = (pageNum - 1) * limitNum;

  const where: any = {};

  if (status) {
    where.status = status;
  }

  if (search) {
    where.OR = [
      { name: { contains: search, mode: 'insensitive' } },
      { email: { contains: search, mode: 'insensitive' } },
      { description: { contains: search, mode: 'insensitive' } },
    ];
  }

  const [clients, total] = await Promise.all([
    prisma.client.findMany({
      where,
      skip,
      take: limitNum,
      orderBy: { [sortBy]: sortOrder },
      include: {
        createdBy: {
          select: { id: true, firstName: true, lastName: true, email: true },
        },
        _count: {
          select: { projects: true, assets: true },
        },
      },
    }),
    prisma.client.count({ where }),
  ]);

  res.json({
    success: true,
    data: clients,
    pagination: {
      page: pageNum,
      limit: limitNum,
      total,
      totalPages: Math.ceil(total / limitNum),
    },
  });
};

export const getClientById = async (req: AuthenticatedRequest, res: Response) => {
  const { id } = req.params;

  const client = await prisma.client.findUnique({
    where: { id },
    include: {
      createdBy: {
        select: { id: true, firstName: true, lastName: true, email: true },
      },
      projects: {
        include: {
          _count: { select: { tasks: true } },
        },
      },
      _count: {
        select: { projects: true, assets: true },
      },
    },
  });

  if (!client) {
    throw createError('Client not found', 404);
  }

  res.json({
    success: true,
    data: client,
  });
};

export const createClient = async (req: AuthenticatedRequest, res: Response) => {
  const data: CreateClientDto = req.body;
  const userId = req.user!.id;

  const client = await prisma.client.create({
    data: {
      ...data,
      createdById: userId,
    },
    include: {
      createdBy: {
        select: { id: true, firstName: true, lastName: true, email: true },
      },
    },
  });

  res.status(201).json({
    success: true,
    data: client,
    message: 'Client created successfully',
  });
};

export const updateClient = async (req: AuthenticatedRequest, res: Response) => {
  const { id } = req.params;
  const data: UpdateClientDto = req.body;

  const existingClient = await prisma.client.findUnique({
    where: { id },
  });

  if (!existingClient) {
    throw createError('Client not found', 404);
  }

  const client = await prisma.client.update({
    where: { id },
    data,
    include: {
      createdBy: {
        select: { id: true, firstName: true, lastName: true, email: true },
      },
    },
  });

  res.json({
    success: true,
    data: client,
    message: 'Client updated successfully',
  });
};

export const deleteClient = async (req: AuthenticatedRequest, res: Response) => {
  const { id } = req.params;

  const existingClient = await prisma.client.findUnique({
    where: { id },
    include: {
      _count: { select: { projects: true } },
    },
  });

  if (!existingClient) {
    throw createError('Client not found', 404);
  }

  if (existingClient._count.projects > 0) {
    throw createError('Cannot delete client with existing projects', 400);
  }

  await prisma.client.delete({
    where: { id },
  });

  res.json({
    success: true,
    message: 'Client deleted successfully',
  });
};

export const getClientProjects = async (req: AuthenticatedRequest, res: Response) => {
  const { id } = req.params;
  const { page = '1', limit = '10', sortBy = 'createdAt', sortOrder = 'desc' } = req.query;

  const pageNum = parseInt(page as string);
  const limitNum = parseInt(limit as string);
  const skip = (pageNum - 1) * limitNum;

  const client = await prisma.client.findUnique({
    where: { id },
  });

  if (!client) {
    throw createError('Client not found', 404);
  }

  const [projects, total] = await Promise.all([
    prisma.project.findMany({
      where: { clientId: id },
      skip,
      take: limitNum,
      orderBy: { [sortBy as string]: sortOrder },
      include: {
        createdBy: {
          select: { id: true, firstName: true, lastName: true, email: true },
        },
        _count: {
          select: { tasks: true, assets: true },
        },
      },
    }),
    prisma.project.count({ where: { clientId: id } }),
  ]);

  res.json({
    success: true,
    data: projects,
    pagination: {
      page: pageNum,
      limit: limitNum,
      total,
      totalPages: Math.ceil(total / limitNum),
    },
  });
};

export const getClientAssets = async (req: AuthenticatedRequest, res: Response) => {
  const { id } = req.params;
  const { page = '1', limit = '10', sortBy = 'createdAt', sortOrder = 'desc' } = req.query;

  const pageNum = parseInt(page as string);
  const limitNum = parseInt(limit as string);
  const skip = (pageNum - 1) * limitNum;

  const client = await prisma.client.findUnique({
    where: { id },
  });

  if (!client) {
    throw createError('Client not found', 404);
  }

  const [assets, total] = await Promise.all([
    prisma.asset.findMany({
      where: { clientId: id },
      skip,
      take: limitNum,
      orderBy: { [sortBy as string]: sortOrder },
      include: {
        createdBy: {
          select: { id: true, firstName: true, lastName: true, email: true },
        },
      },
    }),
    prisma.asset.count({ where: { clientId: id } }),
  ]);

  res.json({
    success: true,
    data: assets,
    pagination: {
      page: pageNum,
      limit: limitNum,
      total,
      totalPages: Math.ceil(total / limitNum),
    },
  });
};
