import { Response } from 'express';
import { prisma } from '../utils/database';
import { createError } from '../middleware/errorHandler';
import { AuthenticatedRequest, CreateProjectDto, UpdateProjectDto, ProjectQuery } from '../types';

export const getProjects = async (req: AuthenticatedRequest, res: Response) => {
  const { 
    page = '1', 
    limit = '10', 
    sortBy = 'createdAt', 
    sortOrder = 'desc', 
    clientId, 
    status, 
    priority, 
    search 
  }: ProjectQuery = req.query;

  const pageNum = parseInt(page);
  const limitNum = parseInt(limit);
  const skip = (pageNum - 1) * limitNum;

  const where: any = {};

  if (clientId) {
    where.clientId = clientId;
  }

  if (status) {
    where.status = status;
  }

  if (priority) {
    where.priority = priority;
  }

  if (search) {
    where.OR = [
      { name: { contains: search, mode: 'insensitive' } },
      { description: { contains: search, mode: 'insensitive' } },
    ];
  }

  const [projects, total] = await Promise.all([
    prisma.project.findMany({
      where,
      skip,
      take: limitNum,
      orderBy: { [sortBy]: sortOrder },
      include: {
        client: {
          select: { id: true, name: true, logo: true },
        },
        createdBy: {
          select: { id: true, firstName: true, lastName: true, email: true },
        },
        _count: {
          select: { tasks: true, assets: true },
        },
      },
    }),
    prisma.project.count({ where }),
  ]);

  res.json({
    success: true,
    data: projects,
    pagination: {
      page: pageNum,
      limit: limitNum,
      total,
      totalPages: Math.ceil(total / limitNum),
    },
  });
};

export const getProjectById = async (req: AuthenticatedRequest, res: Response) => {
  const { id } = req.params;

  const project = await prisma.project.findUnique({
    where: { id },
    include: {
      client: {
        select: { id: true, name: true, logo: true, email: true },
      },
      createdBy: {
        select: { id: true, firstName: true, lastName: true, email: true },
      },
      tasks: {
        include: {
          assignedTo: {
            select: { id: true, firstName: true, lastName: true, email: true },
          },
        },
      },
      _count: {
        select: { tasks: true, assets: true },
      },
    },
  });

  if (!project) {
    throw createError('Project not found', 404);
  }

  res.json({
    success: true,
    data: project,
  });
};

export const createProject = async (req: AuthenticatedRequest, res: Response) => {
  const data: CreateProjectDto = req.body;
  const userId = req.user!.id;

  // Verify client exists
  const client = await prisma.client.findUnique({
    where: { id: data.clientId },
  });

  if (!client) {
    throw createError('Client not found', 404);
  }

  const project = await prisma.project.create({
    data: {
      ...data,
      startDate: data.startDate ? new Date(data.startDate) : null,
      endDate: data.endDate ? new Date(data.endDate) : null,
      createdById: userId,
    },
    include: {
      client: {
        select: { id: true, name: true, logo: true },
      },
      createdBy: {
        select: { id: true, firstName: true, lastName: true, email: true },
      },
    },
  });

  res.status(201).json({
    success: true,
    data: project,
    message: 'Project created successfully',
  });
};

export const updateProject = async (req: AuthenticatedRequest, res: Response) => {
  const { id } = req.params;
  const data: UpdateProjectDto = req.body;

  const existingProject = await prisma.project.findUnique({
    where: { id },
  });

  if (!existingProject) {
    throw createError('Project not found', 404);
  }

  // If clientId is being updated, verify the new client exists
  if (data.clientId) {
    const client = await prisma.client.findUnique({
      where: { id: data.clientId },
    });

    if (!client) {
      throw createError('Client not found', 404);
    }
  }

  const updateData: any = { ...data };
  if (data.startDate) {
    updateData.startDate = new Date(data.startDate);
  }
  if (data.endDate) {
    updateData.endDate = new Date(data.endDate);
  }

  const project = await prisma.project.update({
    where: { id },
    data: updateData,
    include: {
      client: {
        select: { id: true, name: true, logo: true },
      },
      createdBy: {
        select: { id: true, firstName: true, lastName: true, email: true },
      },
    },
  });

  res.json({
    success: true,
    data: project,
    message: 'Project updated successfully',
  });
};

export const deleteProject = async (req: AuthenticatedRequest, res: Response) => {
  const { id } = req.params;

  const existingProject = await prisma.project.findUnique({
    where: { id },
    include: {
      _count: { select: { tasks: true } },
    },
  });

  if (!existingProject) {
    throw createError('Project not found', 404);
  }

  if (existingProject._count.tasks > 0) {
    throw createError('Cannot delete project with existing tasks', 400);
  }

  await prisma.project.delete({
    where: { id },
  });

  res.json({
    success: true,
    message: 'Project deleted successfully',
  });
};

export const getProjectTasks = async (req: AuthenticatedRequest, res: Response) => {
  const { id } = req.params;
  const { page = '1', limit = '10', sortBy = 'createdAt', sortOrder = 'desc' } = req.query;

  const pageNum = parseInt(page as string);
  const limitNum = parseInt(limit as string);
  const skip = (pageNum - 1) * limitNum;

  const project = await prisma.project.findUnique({
    where: { id },
  });

  if (!project) {
    throw createError('Project not found', 404);
  }

  const [tasks, total] = await Promise.all([
    prisma.task.findMany({
      where: { projectId: id },
      skip,
      take: limitNum,
      orderBy: { [sortBy as string]: sortOrder },
      include: {
        assignedTo: {
          select: { id: true, firstName: true, lastName: true, email: true },
        },
        createdBy: {
          select: { id: true, firstName: true, lastName: true, email: true },
        },
        _count: {
          select: { assets: true },
        },
      },
    }),
    prisma.task.count({ where: { projectId: id } }),
  ]);

  res.json({
    success: true,
    data: tasks,
    pagination: {
      page: pageNum,
      limit: limitNum,
      total,
      totalPages: Math.ceil(total / limitNum),
    },
  });
};

export const getProjectAssets = async (req: AuthenticatedRequest, res: Response) => {
  const { id } = req.params;
  const { page = '1', limit = '10', sortBy = 'createdAt', sortOrder = 'desc' } = req.query;

  const pageNum = parseInt(page as string);
  const limitNum = parseInt(limit as string);
  const skip = (pageNum - 1) * limitNum;

  const project = await prisma.project.findUnique({
    where: { id },
  });

  if (!project) {
    throw createError('Project not found', 404);
  }

  const [assets, total] = await Promise.all([
    prisma.asset.findMany({
      where: { projectId: id },
      skip,
      take: limitNum,
      orderBy: { [sortBy as string]: sortOrder },
      include: {
        createdBy: {
          select: { id: true, firstName: true, lastName: true, email: true },
        },
      },
    }),
    prisma.asset.count({ where: { projectId: id } }),
  ]);

  res.json({
    success: true,
    data: assets,
    pagination: {
      page: pageNum,
      limit: limitNum,
      total,
      totalPages: Math.ceil(total / limitNum),
    },
  });
};
