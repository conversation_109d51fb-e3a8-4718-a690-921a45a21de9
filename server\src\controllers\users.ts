import { Response } from 'express';
import { prisma } from '../utils/database';
import { createError } from '../middleware/errorHandler';
import { AuthenticatedRequest, PaginationQuery } from '../types';

export const getUsers = async (req: AuthenticatedRequest, res: Response) => {
  const { page = '1', limit = '10', sortBy = 'createdAt', sortOrder = 'desc' }: PaginationQuery = req.query;

  const pageNum = parseInt(page);
  const limitNum = parseInt(limit);
  const skip = (pageNum - 1) * limitNum;

  const [users, total] = await Promise.all([
    prisma.user.findMany({
      skip,
      take: limitNum,
      orderBy: { [sortBy]: sortOrder },
      select: {
        id: true,
        email: true,
        firstName: true,
        lastName: true,
        avatar: true,
        role: true,
        isActive: true,
        createdAt: true,
        updatedAt: true,
      },
    }),
    prisma.user.count(),
  ]);

  res.json({
    success: true,
    data: users,
    pagination: {
      page: pageNum,
      limit: limitNum,
      total,
      totalPages: Math.ceil(total / limitNum),
    },
  });
};

export const getCurrentUser = async (req: AuthenticatedRequest, res: Response) => {
  const user = req.user!;

  const userWithStats = await prisma.user.findUnique({
    where: { id: user.id },
    select: {
      id: true,
      email: true,
      firstName: true,
      lastName: true,
      avatar: true,
      role: true,
      isActive: true,
      createdAt: true,
      updatedAt: true,
      _count: {
        select: {
          createdClients: true,
          createdProjects: true,
          assignedTasks: true,
          createdTasks: true,
          createdAssets: true,
        },
      },
    },
  });

  res.json({
    success: true,
    data: userWithStats,
  });
};

export const getUserById = async (req: AuthenticatedRequest, res: Response) => {
  const { id } = req.params;

  const user = await prisma.user.findUnique({
    where: { id },
    select: {
      id: true,
      email: true,
      firstName: true,
      lastName: true,
      avatar: true,
      role: true,
      isActive: true,
      createdAt: true,
      updatedAt: true,
      _count: {
        select: {
          createdClients: true,
          createdProjects: true,
          assignedTasks: true,
          createdTasks: true,
          createdAssets: true,
        },
      },
    },
  });

  if (!user) {
    throw createError('User not found', 404);
  }

  res.json({
    success: true,
    data: user,
  });
};

export const updateCurrentUser = async (req: AuthenticatedRequest, res: Response) => {
  const userId = req.user!.id;
  const { firstName, lastName, avatar } = req.body;

  const user = await prisma.user.update({
    where: { id: userId },
    data: {
      firstName: firstName || undefined,
      lastName: lastName || undefined,
      avatar: avatar || undefined,
    },
    select: {
      id: true,
      email: true,
      firstName: true,
      lastName: true,
      avatar: true,
      role: true,
      isActive: true,
      createdAt: true,
      updatedAt: true,
    },
  });

  res.json({
    success: true,
    data: user,
    message: 'Profile updated successfully',
  });
};

export const updateUser = async (req: AuthenticatedRequest, res: Response) => {
  const { id } = req.params;
  const { firstName, lastName, avatar, role, isActive } = req.body;
  const currentUser = req.user!;

  // Only admins can update other users
  if (currentUser.role !== 'ADMIN' && currentUser.id !== id) {
    throw createError('Insufficient permissions', 403);
  }

  // Only admins can change roles and active status
  const updateData: any = {
    firstName: firstName || undefined,
    lastName: lastName || undefined,
    avatar: avatar || undefined,
  };

  if (currentUser.role === 'ADMIN') {
    if (role !== undefined) updateData.role = role;
    if (isActive !== undefined) updateData.isActive = isActive;
  }

  const user = await prisma.user.update({
    where: { id },
    data: updateData,
    select: {
      id: true,
      email: true,
      firstName: true,
      lastName: true,
      avatar: true,
      role: true,
      isActive: true,
      createdAt: true,
      updatedAt: true,
    },
  });

  res.json({
    success: true,
    data: user,
    message: 'User updated successfully',
  });
};
